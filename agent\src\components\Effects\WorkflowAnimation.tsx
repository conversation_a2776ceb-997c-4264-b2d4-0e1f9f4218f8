import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    MessageSquare,
    Video,
    Mic,
    Music,
    Image,
    Instagram,
    Youtube,
    Twitter,
    Sparkles
} from 'lucide-react';

interface WorkflowNode {
    id: string;
    label: string;
    icon: React.ComponentType<any>;
    color: string;
    position: { x: number; y: number };
    delay: number;
    category: 'input' | 'processing' | 'output' | 'social';
}

interface WorkflowAnimationProps {
    isVisible: boolean;
    prompt?: string;
    onComplete?: () => void;
}

export const WorkflowAnimation: React.FC<WorkflowAnimationProps> = ({
    isVisible,
    prompt,
    onComplete
}) => {
    const [activeConnections, setActiveConnections] = useState<string[]>([]);
    const [currentStep, setCurrentStep] = useState(0);

    const nodes: WorkflowNode[] = [
        // Input
        {
            id: 'prompt',
            label: 'Your Prompt',
            icon: MessageSquare,
            color: 'bg-blue-500',
            position: { x: 50, y: 250 },
            delay: 0,
            category: 'input'
        },
        // Processing nodes
        {
            id: 'ai-script',
            label: 'AI Script Generator',
            icon: Sparkles,
            color: 'bg-purple-500',
            position: { x: 200, y: 150 },
            delay: 1000,
            category: 'processing'
        },
        {
            id: 'voiceover',
            label: 'AI Voiceover',
            icon: Mic,
            color: 'bg-green-500',
            position: { x: 200, y: 250 },
            delay: 1500,
            category: 'processing'
        },
        {
            id: 'music',
            label: 'Background Music',
            icon: Music,
            color: 'bg-yellow-500',
            position: { x: 200, y: 350 },
            delay: 2000,
            category: 'processing'
        },
        {
            id: 'visuals',
            label: 'Visual Generator',
            icon: Image,
            color: 'bg-pink-500',
            position: { x: 350, y: 200 },
            delay: 2500,
            category: 'processing'
        },
        {
            id: 'video',
            label: 'Video Renderer',
            icon: Video,
            color: 'bg-red-500',
            position: { x: 500, y: 250 },
            delay: 3000,
            category: 'output'
        },
        // Social platforms
        {
            id: 'instagram',
            label: 'Instagram',
            icon: Instagram,
            color: 'bg-gradient-to-r from-purple-500 to-pink-500',
            position: { x: 650, y: 150 },
            delay: 4000,
            category: 'social'
        },
        {
            id: 'youtube',
            label: 'YouTube',
            icon: Youtube,
            color: 'bg-red-600',
            position: { x: 650, y: 250 },
            delay: 4200,
            category: 'social'
        },
        {
            id: 'twitter',
            label: 'Twitter',
            icon: Twitter,
            color: 'bg-blue-400',
            position: { x: 650, y: 350 },
            delay: 4400,
            category: 'social'
        }
    ];

    const connections = [
        { from: 'prompt', to: 'ai-script', delay: 1200 },
        { from: 'prompt', to: 'voiceover', delay: 1700 },
        { from: 'prompt', to: 'music', delay: 2200 },
        { from: 'ai-script', to: 'visuals', delay: 2700 },
        { from: 'voiceover', to: 'visuals', delay: 2900 },
        { from: 'music', to: 'visuals', delay: 3100 },
        { from: 'visuals', to: 'video', delay: 3200 },
        { from: 'video', to: 'instagram', delay: 4100 },
        { from: 'video', to: 'youtube', delay: 4300 },
        { from: 'video', to: 'twitter', delay: 4500 }
    ];

    useEffect(() => {
        if (!isVisible) {
            setActiveConnections([]);
            setCurrentStep(0);
            return;
        }

        const timers: number[] = [];

        // Animate connections
        connections.forEach((connection, index) => {
            const timer = setTimeout(() => {
                setActiveConnections(prev => [...prev, `${connection.from}-${connection.to}`]);
                setCurrentStep(index + 1);
            }, connection.delay);
            timers.push(timer);
        });

        // Complete animation
        const completeTimer = setTimeout(() => {
            onComplete?.();
        }, 6000);
        timers.push(completeTimer); return () => {
            timers.forEach(timer => clearTimeout(timer));
        };
    }, [isVisible, onComplete]);

    const getNodePosition = (nodeId: string) => {
        const node = nodes.find(n => n.id === nodeId);
        return node ? node.position : { x: 0, y: 0 };
    };

    const renderConnection = (from: string, to: string, isActive: boolean) => {
        const fromPos = getNodePosition(from);
        const toPos = getNodePosition(to);

        const deltaX = toPos.x - fromPos.x;
        const deltaY = toPos.y - fromPos.y;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

        return (
            <motion.div
                key={`${from}-${to}`}
                className="absolute"
                style={{
                    left: fromPos.x + 40,
                    top: fromPos.y + 40,
                    width: distance,
                    height: 2,
                    transformOrigin: '0 50%',
                    transform: `rotate(${angle}deg)`,
                }}
            >
                <motion.div
                    className="h-full bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"
                    initial={{ scaleX: 0, opacity: 0 }}
                    animate={isActive ? { scaleX: 1, opacity: 1 } : {}}
                    transition={{ duration: 0.8, ease: "easeInOut" }}
                />

                {/* Animated pulse */}
                {isActive && (
                    <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"
                        animate={{
                            scaleX: [1, 1.1, 1],
                            opacity: [0.7, 1, 0.7]
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                )}
            </motion.div>
        );
    };

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="relative w-full max-w-5xl h-96 bg-white dark:bg-dark-800 rounded-2xl shadow-2xl overflow-hidden">
                {/* Header */}
                <div className="absolute top-4 left-4 right-4 z-10">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                                Building Your Content Workflow
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {prompt ? `"${prompt.slice(0, 50)}..."` : 'Processing your request...'}
                            </p>
                        </div>
                        <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                                Step {currentStep} of {connections.length}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Workflow Canvas */}
                <div className="relative w-full h-full pt-20 pb-4">
                    {/* Render connections */}
                    {connections.map((connection) =>
                        renderConnection(
                            connection.from,
                            connection.to,
                            activeConnections.includes(`${connection.from}-${connection.to}`)
                        )
                    )}

                    {/* Render nodes */}          {nodes.map((node) => {
                        const Icon = node.icon;

                        return (
                            <motion.div
                                key={node.id}
                                className="absolute"
                                style={{
                                    left: node.position.x,
                                    top: node.position.y,
                                }}
                                initial={{ scale: 0, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{
                                    delay: node.delay / 1000,
                                    duration: 0.5,
                                    type: "spring",
                                    stiffness: 200,
                                    damping: 20
                                }}
                            >
                                <div className={`w-20 h-20 ${node.color} rounded-xl flex items-center justify-center shadow-lg relative`}>
                                    <Icon className="w-8 h-8 text-white" />

                                    {/* Pulse effect for active nodes */}
                                    <motion.div
                                        className={`absolute inset-0 ${node.color} rounded-xl opacity-30`}
                                        animate={{
                                            scale: [1, 1.2, 1],
                                            opacity: [0.3, 0.6, 0.3]
                                        }}
                                        transition={{
                                            duration: 2,
                                            repeat: Infinity,
                                            ease: "easeInOut",
                                            delay: node.delay / 1000
                                        }}
                                    />
                                </div>

                                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-dark-700 px-2 py-1 rounded shadow">
                                        {node.label}
                                    </span>
                                </div>
                            </motion.div>
                        );
                    })}

                    {/* Floating particles */}
                    {[...Array(20)].map((_, index) => (
                        <motion.div
                            key={index}
                            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-50"
                            style={{
                                left: Math.random() * 100 + '%',
                                top: Math.random() * 100 + '%',
                            }}
                            animate={{
                                y: [0, -20, 0],
                                x: [0, Math.random() * 20 - 10, 0],
                                opacity: [0.5, 1, 0.5]
                            }}
                            transition={{
                                duration: 3 + Math.random() * 2,
                                repeat: Infinity,
                                ease: "easeInOut",
                                delay: Math.random() * 2
                            }}
                        />
                    ))}
                </div>

                {/* Progress bar */}
                <div className="absolute bottom-4 left-4 right-4">
                    <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                        <motion.div
                            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${(currentStep / connections.length) * 100}%` }}
                            transition={{ duration: 0.5 }}
                        />
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                        {currentStep === connections.length
                            ? 'Workflow complete! Your content is ready.'
                            : 'Connecting services and generating content...'}
                    </p>
                </div>
            </div>
        </div>
    );
};
