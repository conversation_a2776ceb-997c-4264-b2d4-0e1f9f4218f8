import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { WorkflowAnimation } from '../Effects/WorkflowAnimation';
import { useChatStore } from '../../store/chatStore';
import { Sparkles, Video, Wand2 } from 'lucide-react';

export const ChatInterface: React.FC = () => {
  const {
    currentSession,
    addMessage,
    generateContent,
    isGenerating,
    createSession
  } = useChatStore();

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [showWorkflow, setShowWorkflow] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // Create initial session if none exists
  useEffect(() => {
    if (!currentSession) {
      createSession();
    }
  }, [currentSession, createSession]);

  const handleSendMessage = async (message: string) => {
    if (!currentSession) return;

    // Add user message
    addMessage(message, 'user');

    // Set current prompt and show workflow animation
    setCurrentPrompt(message);
    setShowWorkflow(true);

    // Simulate AI processing and generate content
    try {
      await generateContent({
        text: message,
        tone: 'professional',
        platform: ['instagram', 'tiktok', 'youtube']
      });
    } catch (error) {
      addMessage(
        'I apologize, but I encountered an error while processing your request. Please try again.',
        'assistant'
      );
    }
  };

  const handleWorkflowComplete = () => {
    setShowWorkflow(false);
  };

  const EmptyState = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex-1 flex items-center justify-center p-8"
    >
      <div className="text-center max-w-md">
        <motion.div
          animate={{
            rotate: [0, 5, -5, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            repeatType: "reverse"
          }}
          className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
        >
          <Sparkles className="w-8 h-8 text-white" />
        </motion.div>

        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
          Welcome to ContentAI
        </h2>

        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Transform your ideas into engaging social media content with AI-powered video generation,
          voiceovers, and automated posting across all platforms.
        </p>

        <div className="grid grid-cols-1 gap-3">
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="flex items-center space-x-3 p-4 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-lg"
          >
            <Video className="w-6 h-6 text-primary-500" />
            <div className="text-left">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">
                AI Video Generation
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Create professional videos with AI voiceovers
              </p>
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="flex items-center space-x-3 p-4 bg-gradient-to-r from-secondary-50 to-primary-50 dark:from-secondary-900/20 dark:to-primary-900/20 rounded-lg"
          >
            <Wand2 className="w-6 h-6 text-secondary-500" />
            <div className="text-left">
              <h3 className="font-medium text-gray-900 dark:text-gray-100">
                Smart Content Creation
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Generate engaging copy tailored to your audience
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );

  if (!currentSession) {
    return <EmptyState />;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Workflow Animation */}
      <WorkflowAnimation
        isVisible={showWorkflow}
        prompt={currentPrompt}
        onComplete={handleWorkflowComplete}
      />

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto">
        {currentSession.messages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="space-y-0">
            <AnimatePresence>
              {currentSession.messages.map((message, index) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isLast={index === currentSession.messages.length - 1}
                />
              ))}
            </AnimatePresence>

            {/* Typing Indicator */}
            {isGenerating && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="flex gap-4 p-6 bg-gray-50 dark:bg-dark-800/50"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-accent-500 to-secondary-500 rounded-full flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      ContentAI
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      is creating your content...
                    </span>
                  </div>
                  <div className="flex space-x-1">
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                      className="w-2 h-2 bg-primary-500 rounded-full"
                    />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                      className="w-2 h-2 bg-accent-500 rounded-full"
                    />
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                      className="w-2 h-2 bg-secondary-500 rounded-full"
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isGenerating}
      />
    </div>
  );
};
