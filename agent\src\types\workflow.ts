// Workflow Node Types
export interface WorkflowNode {
    id: string;
    type: 'start' | 'script' | 'voiceover' | 'visuals' | 'video' | 'social' | 'trigger';
    position: { x: number; y: number };
    data: {
        label: string;
        description?: string;
        config?: Record<string, any>;
        completed?: boolean;
        active?: boolean;
    };
    inputs?: NodeConnection[];
    outputs?: NodeConnection[];
}

export interface NodeConnection {
    id: string;
    type: 'input' | 'output';
    position: 'top' | 'bottom' | 'left' | 'right';
}

export interface WorkflowEdge {
    id: string;
    source: string;
    target: string;
    sourceHandle?: string;
    targetHandle?: string;
    animated?: boolean;
}

export interface WorkflowData {
    id: string;
    name: string;
    description?: string;
    nodes: WorkflowNode[];
    edges: WorkflowEdge[];
    createdAt: Date;
    updatedAt: Date;
}

export interface NodeTypeConfig {
    type: string;
    label: string;
    description: string;
    icon: string;
    color: string;
    category: 'input' | 'processing' | 'output' | 'social';
    inputs: number;
    outputs: number;
    configFields?: NodeConfigField[];
}

export interface NodeConfigField {
    key: string;
    label: string;
    type: 'text' | 'textarea' | 'select' | 'checkbox' | 'number';
    required?: boolean;
    options?: { label: string; value: string }[];
    placeholder?: string;
    description?: string;
}

export interface ExecutionResult {
    nodeId: string;
    status: 'success' | 'error' | 'running';
    data?: any;
    error?: string;
    timestamp: Date;
}
