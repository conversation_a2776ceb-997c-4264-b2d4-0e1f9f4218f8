import { create } from 'zustand';
import type { WorkflowData, WorkflowNode, WorkflowEdge, ExecutionResult, NodeTypeConfig } from '../types';

interface WorkflowStore {
    workflows: WorkflowData[];
    currentWorkflow: WorkflowData | null;
    selectedNode: WorkflowNode | null;
    isExecuting: boolean;
    executionResults: ExecutionResult[];
    nodeTypes: NodeTypeConfig[];
    draggedNodeType: string | null;

    // Workflow actions
    createWorkflow: (name: string, description?: string) => void;
    selectWorkflow: (workflowId: string) => void;
    deleteWorkflow: (workflowId: string) => void;
    updateWorkflow: (workflowId: string, updates: Partial<WorkflowData>) => void;

    // Node actions
    addNode: (node: Omit<WorkflowNode, 'id'>) => void;
    updateNode: (nodeId: string, updates: Partial<WorkflowNode>) => void;
    deleteNode: (nodeId: string) => void;
    selectNode: (nodeId: string | null) => void;

    // Edge actions
    addEdge: (edge: Omit<WorkflowEdge, 'id'>) => void;
    deleteEdge: (edgeId: string) => void;    // Execution actions
    executeWorkflow: () => Promise<void>;
    executeNode: (nodeId: string) => Promise<void>;
    executeNodeSequence: (nodeId: string, visited: Set<string>) => Promise<void>;
    setExecuting: (executing: boolean) => void;
    addExecutionResult: (result: ExecutionResult) => void;
    clearExecutionResults: () => void;

    // Drag and drop actions
    setDraggedNodeType: (nodeType: string | null) => void;
}

// Default node types configuration
const defaultNodeTypes: NodeTypeConfig[] = [
    {
        type: 'start',
        label: 'Start',
        description: 'Workflow trigger point',
        icon: 'Play',
        color: 'bg-green-500',
        category: 'input',
        inputs: 0,
        outputs: 1,
        configFields: [
            {
                key: 'prompt',
                label: 'Initial Prompt',
                type: 'textarea',
                required: true,
                placeholder: 'Enter your content creation prompt...',
                description: 'The initial prompt that will trigger the workflow'
            }
        ]
    },
    {
        type: 'script',
        label: 'AI Script Generator',
        description: 'Generate content script using AI',
        icon: 'FileText',
        color: 'bg-purple-500',
        category: 'processing',
        inputs: 1,
        outputs: 1,
        configFields: [
            {
                key: 'tone',
                label: 'Tone',
                type: 'select',
                options: [
                    { label: 'Professional', value: 'professional' },
                    { label: 'Casual', value: 'casual' },
                    { label: 'Energetic', value: 'energetic' },
                    { label: 'Friendly', value: 'friendly' }
                ],
                description: 'Choose the tone for your script'
            },
            {
                key: 'length',
                label: 'Script Length',
                type: 'select',
                options: [
                    { label: 'Short (30-60s)', value: 'short' },
                    { label: 'Medium (1-2min)', value: 'medium' },
                    { label: 'Long (2-5min)', value: 'long' }
                ],
                description: 'Target length for the generated script'
            }
        ]
    },
    {
        type: 'voiceover',
        label: 'AI Voiceover',
        description: 'Generate voiceover from script',
        icon: 'Mic',
        color: 'bg-blue-500',
        category: 'processing',
        inputs: 1,
        outputs: 1,
        configFields: [
            {
                key: 'voice',
                label: 'Voice Type',
                type: 'select',
                options: [
                    { label: 'Male Professional', value: 'male-professional' },
                    { label: 'Female Professional', value: 'female-professional' },
                    { label: 'Male Casual', value: 'male-casual' },
                    { label: 'Female Casual', value: 'female-casual' }
                ],
                description: 'Select the voice for your voiceover'
            },
            {
                key: 'speed',
                label: 'Speaking Speed',
                type: 'select',
                options: [
                    { label: 'Slow', value: '0.8' },
                    { label: 'Normal', value: '1.0' },
                    { label: 'Fast', value: '1.2' }
                ],
                description: 'Adjust the speaking speed'
            }
        ]
    },
    {
        type: 'visuals',
        label: 'Visual Generator',
        description: 'Generate visuals and animations',
        icon: 'Image',
        color: 'bg-pink-500',
        category: 'processing',
        inputs: 1,
        outputs: 1,
        configFields: [
            {
                key: 'style',
                label: 'Visual Style',
                type: 'select',
                options: [
                    { label: 'Modern', value: 'modern' },
                    { label: 'Minimalist', value: 'minimalist' },
                    { label: 'Vibrant', value: 'vibrant' },
                    { label: 'Professional', value: 'professional' }
                ],
                description: 'Choose the visual style'
            },
            {
                key: 'animation',
                label: 'Animation Level',
                type: 'select',
                options: [
                    { label: 'Static', value: 'static' },
                    { label: 'Subtle', value: 'subtle' },
                    { label: 'Dynamic', value: 'dynamic' }
                ],
                description: 'Level of animation in visuals'
            }
        ]
    },
    {
        type: 'video',
        label: 'Video Renderer',
        description: 'Compile everything into final video',
        icon: 'Video',
        color: 'bg-red-500',
        category: 'output',
        inputs: 3,
        outputs: 1,
        configFields: [
            {
                key: 'resolution',
                label: 'Resolution',
                type: 'select',
                options: [
                    { label: '720p', value: '720p' },
                    { label: '1080p', value: '1080p' },
                    { label: '4K', value: '4k' }
                ],
                description: 'Video output resolution'
            },
            {
                key: 'format',
                label: 'Format',
                type: 'select',
                options: [
                    { label: 'MP4', value: 'mp4' },
                    { label: 'MOV', value: 'mov' },
                    { label: 'AVI', value: 'avi' }
                ],
                description: 'Video file format'
            }
        ]
    },
    {
        type: 'social',
        label: 'Social Media Post',
        description: 'Post to social media platforms',
        icon: 'Share2',
        color: 'bg-indigo-500',
        category: 'social',
        inputs: 1,
        outputs: 0,
        configFields: [
            {
                key: 'platforms',
                label: 'Platforms',
                type: 'select',
                options: [
                    { label: 'Instagram', value: 'instagram' },
                    { label: 'TikTok', value: 'tiktok' },
                    { label: 'YouTube', value: 'youtube' },
                    { label: 'Twitter', value: 'twitter' }
                ],
                description: 'Select platforms to post to'
            },
            {
                key: 'schedule',
                label: 'Schedule',
                type: 'select',
                options: [
                    { label: 'Post Now', value: 'now' },
                    { label: 'Schedule Later', value: 'schedule' }
                ],
                description: 'When to post the content'
            }
        ]
    }
];

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
    workflows: [],
    currentWorkflow: null,
    selectedNode: null,
    isExecuting: false,
    executionResults: [],
    nodeTypes: defaultNodeTypes,
    draggedNodeType: null,

    createWorkflow: (name: string, description?: string) => {
        const newWorkflow: WorkflowData = {
            id: Date.now().toString(),
            name,
            description,
            nodes: [],
            edges: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };

        set(state => ({
            workflows: [newWorkflow, ...state.workflows],
            currentWorkflow: newWorkflow
        }));
    },

    selectWorkflow: (workflowId: string) => {
        const { workflows } = get();
        const workflow = workflows.find(w => w.id === workflowId);
        if (workflow) {
            set({ currentWorkflow: workflow });
        }
    },

    deleteWorkflow: (workflowId: string) => {
        set(state => {
            const newWorkflows = state.workflows.filter(w => w.id !== workflowId);
            const newCurrentWorkflow = state.currentWorkflow?.id === workflowId
                ? (newWorkflows[0] || null)
                : state.currentWorkflow;

            return {
                workflows: newWorkflows,
                currentWorkflow: newCurrentWorkflow
            };
        });
    },

    updateWorkflow: (workflowId: string, updates: Partial<WorkflowData>) => {
        set(state => {
            const updatedWorkflows = state.workflows.map(workflow =>
                workflow.id === workflowId
                    ? { ...workflow, ...updates, updatedAt: new Date() }
                    : workflow
            );

            const updatedCurrentWorkflow = state.currentWorkflow?.id === workflowId
                ? { ...state.currentWorkflow, ...updates, updatedAt: new Date() }
                : state.currentWorkflow;

            return {
                workflows: updatedWorkflows,
                currentWorkflow: updatedCurrentWorkflow
            };
        });
    },

    addNode: (nodeData: Omit<WorkflowNode, 'id'>) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const newNode: WorkflowNode = {
            ...nodeData,
            id: `node-${Date.now()}`
        };

        const updatedNodes = [...currentWorkflow.nodes, newNode];

        get().updateWorkflow(currentWorkflow.id, { nodes: updatedNodes });
    },

    updateNode: (nodeId: string, updates: Partial<WorkflowNode>) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const updatedNodes = currentWorkflow.nodes.map(node =>
            node.id === nodeId ? { ...node, ...updates } : node
        );

        get().updateWorkflow(currentWorkflow.id, { nodes: updatedNodes });
    },

    deleteNode: (nodeId: string) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const updatedNodes = currentWorkflow.nodes.filter(node => node.id !== nodeId);
        const updatedEdges = currentWorkflow.edges.filter(
            edge => edge.source !== nodeId && edge.target !== nodeId
        );

        get().updateWorkflow(currentWorkflow.id, {
            nodes: updatedNodes,
            edges: updatedEdges
        });

        set({ selectedNode: null });
    },

    selectNode: (nodeId: string | null) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow || !nodeId) {
            set({ selectedNode: null });
            return;
        }

        const node = currentWorkflow.nodes.find(n => n.id === nodeId);
        set({ selectedNode: node || null });
    },

    addEdge: (edgeData: Omit<WorkflowEdge, 'id'>) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const newEdge: WorkflowEdge = {
            ...edgeData,
            id: `edge-${Date.now()}`
        };

        const updatedEdges = [...currentWorkflow.edges, newEdge];

        get().updateWorkflow(currentWorkflow.id, { edges: updatedEdges });
    },

    deleteEdge: (edgeId: string) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const updatedEdges = currentWorkflow.edges.filter(edge => edge.id !== edgeId);

        get().updateWorkflow(currentWorkflow.id, { edges: updatedEdges });
    }, executeWorkflow: async () => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        set({ isExecuting: true });
        get().clearExecutionResults();

        try {
            // Reset all nodes to inactive and not completed
            currentWorkflow.nodes.forEach(node => {
                get().updateNode(node.id, {
                    data: { ...node.data, active: false, completed: false }
                });
            });

            // Find start node
            const startNode = currentWorkflow.nodes.find(node => node.type === 'start');
            if (!startNode) {
                throw new Error('No start node found');
            }

            // Execute nodes following the workflow path
            await get().executeNodeSequence(startNode.id, new Set());

        } catch (error) {
            console.error('Workflow execution failed:', error);
        } finally {
            set({ isExecuting: false });
        }
    },

    executeNodeSequence: async (nodeId: string, visited: Set<string>) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow || visited.has(nodeId)) return;

        visited.add(nodeId);

        // Execute current node
        await get().executeNode(nodeId);

        // Find all outgoing edges from this node
        const outgoingEdges = currentWorkflow.edges.filter(edge => edge.source === nodeId);

        // Execute connected nodes in parallel (or sequentially based on your preference)
        for (const edge of outgoingEdges) {
            await get().executeNodeSequence(edge.target, visited);
        }
    }, executeNode: async (nodeId: string) => {
        const { currentWorkflow } = get();
        if (!currentWorkflow) return;

        const node = currentWorkflow.nodes.find(n => n.id === nodeId);
        if (!node) return;

        console.log(`Executing node: ${node.data.label}`);

        // Mark node as active
        get().updateNode(nodeId, { data: { ...node.data, active: true } });

        try {            // Simulate different execution times based on node type
            const executionTime = {
                start: 1000,
                script: 3000,
                voiceover: 4000,
                visuals: 5000,
                video: 6000,
                social: 2000,
                trigger: 1500
            }[node.type] || 2000;

            await new Promise(resolve => setTimeout(resolve, executionTime));

            // Mark as completed
            get().updateNode(nodeId, {
                data: { ...node.data, active: false, completed: true }
            });

            // Add execution result
            get().addExecutionResult({
                nodeId,
                status: 'success',
                data: {
                    message: `${node.data.label} completed successfully`,
                    executionTime: executionTime
                },
                timestamp: new Date()
            });

        } catch (error) {
            get().updateNode(nodeId, {
                data: { ...node.data, active: false }
            });

            get().addExecutionResult({
                nodeId,
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date()
            });

            throw error; // Re-throw to stop workflow execution
        }
    },

    setExecuting: (executing: boolean) => {
        set({ isExecuting: executing });
    },

    addExecutionResult: (result: ExecutionResult) => {
        set(state => ({
            executionResults: [...state.executionResults, result]
        }));
    },

    clearExecutionResults: () => {
        set({ executionResults: [] });
    },

    setDraggedNodeType: (nodeType: string | null) => {
        set({ draggedNodeType: nodeType });
    }
}));
