import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Play,
    Square,
    Plus,
    ArrowRight,
    Zap,
    Settings
} from 'lucide-react';
import { useWorkflowStore } from '../../store/workflowStore';
import { WorkflowNode } from './WorkflowNode';
import { NodeConfigPanel } from './NodeConfigPanel';
import { ConnectionLine } from './ConnectionLine';
import { NodesToolbar } from './NodesToolbar';
import type { WorkflowNode as WorkflowNodeType } from '../../types';

export const WorkflowBuilder: React.FC = () => {
    const {
        currentWorkflow,
        selectedNode,
        isExecuting,
        addNode,
        selectNode,
        deleteNode,
        addEdge,
        deleteEdge,
        draggedNodeType,
        setDraggedNodeType,
        executeWorkflow
    } = useWorkflowStore();

    const [connecting, setConnecting] = useState<{ nodeId: string; type: 'output' } | null>(null);
    const canvasRef = useRef<HTMLDivElement>(null);

    // Create initial workflow if none exists
    useEffect(() => {
        if (!currentWorkflow) {
            // This will be handled by the sidebar now
        }
    }, [currentWorkflow]);

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'copy';
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();

        if (!draggedNodeType || !canvasRef.current) return;

        const rect = canvasRef.current.getBoundingClientRect();
        const nodeTypeConfig = useWorkflowStore.getState().nodeTypes.find(nt => nt.type === draggedNodeType);

        if (nodeTypeConfig) {
            const newNode: Omit<WorkflowNodeType, 'id'> = {
                type: draggedNodeType as any,
                position: {
                    x: e.clientX - rect.left - 90, // Center the node
                    y: e.clientY - rect.top - 50
                },
                data: {
                    label: nodeTypeConfig.label,
                    description: nodeTypeConfig.description,
                    config: {},
                    completed: false,
                    active: false
                }
            };

            addNode(newNode);
        }

        setDraggedNodeType(null);
    };

    const handleNodeClick = (nodeId: string) => {
        selectNode(nodeId);
    };

    const handleCanvasClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            selectNode(null);
            setConnecting(null);
        }
    }; const handleExecute = async () => {
        if (!isExecuting && currentWorkflow && currentWorkflow.nodes.length > 0) {
            await executeWorkflow();
        }
    };

    const handleNodeConnect = (nodeId: string, type: 'input' | 'output') => {
        if (!connecting) {
            // Start a new connection from an output
            if (type === 'output') {
                setConnecting({ nodeId, type });
            }
        } else {
            // Complete the connection
            if (type === 'input' && connecting.nodeId !== nodeId) {
                // Create a new edge
                addEdge({
                    source: connecting.nodeId,
                    target: nodeId,
                    animated: true
                });
                setConnecting(null);
            } else {
                // Cancel connection if trying to connect to same node or invalid connection
                setConnecting(null);
            }
        }
    }; const handleNodePositionChange = (nodeId: string, position: { x: number; y: number }) => {
        const { updateNode } = useWorkflowStore.getState();
        updateNode(nodeId, { position });
    };

    const handleNodeDelete = (nodeId: string) => {
        deleteNode(nodeId);
        if (selectedNode?.id === nodeId) {
            selectNode(null);
        }
    };

    const handleEdgeDelete = (edgeId: string) => {
        deleteEdge(edgeId);
    }; const EmptyState = () => (
        <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
                <motion.div
                    animate={{
                        rotate: [0, 5, -5, 0],
                        scale: [1, 1.1, 1]
                    }}
                    transition={{
                        duration: 4,
                        repeat: Infinity,
                        repeatType: "reverse"
                    }}
                    className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-6"
                >
                    <Zap className="w-8 h-8 text-white" />
                </motion.div>

                <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                    Build Your Content Workflow
                </h2>

                <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Drag and drop nodes from the right toolbar to create your custom content generation workflow.
                    Connect nodes to define the flow of your content creation process.
                </p>

                <div className="grid grid-cols-1 gap-3">
                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="flex items-center space-x-3 p-4 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-lg"
                    >
                        <Play className="w-6 h-6 text-primary-500" />
                        <div className="text-left">
                            <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                Start with a Trigger
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Drag the Start node from the right panel to begin
                            </p>
                        </div>
                    </motion.div>

                    <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="flex items-center space-x-3 p-4 bg-gradient-to-r from-secondary-50 to-primary-50 dark:from-secondary-900/20 dark:to-primary-900/20 rounded-lg"
                    >
                        <ArrowRight className="w-6 h-6 text-secondary-500" />
                        <div className="text-left">
                            <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                Connect Your Nodes
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Click output connections and drag to input connections
                            </p>
                        </div>
                    </motion.div>
                </div>
            </div>
        </div>
    ); if (!currentWorkflow) {
        return (
            <div className="flex h-full bg-gray-50 dark:bg-dark-900">
                {/* Canvas Area with Empty State */}
                <div className="flex-1 flex flex-col">
                    {/* Execution Controls */}
                    <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    disabled={true}
                                    className="flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all bg-gray-200 dark:bg-dark-600 text-gray-400 cursor-not-allowed"
                                >
                                    <Play className="w-4 h-4" />
                                    <span>Start Workflow</span>
                                </motion.button>

                                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                    <span>No workflow selected</span>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 text-gray-600 dark:text-gray-400 transition-colors">
                                    <Settings className="w-4 h-4" />
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Empty State */}
                    <EmptyState />
                </div>

                {/* Nodes Toolbar */}
                <NodesToolbar />
            </div>
        );
    } return (
        <div className="flex h-full bg-gray-50 dark:bg-dark-900">
            {/* Canvas Area */}
            <div className="flex-1 flex flex-col">
                {/* Execution Controls */}
                <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={handleExecute}
                                disabled={isExecuting || !currentWorkflow || currentWorkflow.nodes.length === 0}
                                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all ${isExecuting || !currentWorkflow || currentWorkflow.nodes.length === 0
                                    ? 'bg-gray-200 dark:bg-dark-600 text-gray-400 cursor-not-allowed'
                                    : 'bg-primary-500 hover:bg-primary-600 text-white shadow-lg hover:shadow-xl'
                                    }`}
                            >
                                {isExecuting ? (
                                    <>
                                        <motion.div
                                            animate={{ rotate: 360 }}
                                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                        >
                                            <Square className="w-4 h-4" />
                                        </motion.div>
                                        <span>Running...</span>
                                    </>
                                ) : (
                                    <>
                                        <Play className="w-4 h-4" />
                                        <span>Start Workflow</span>
                                    </>
                                )}
                            </motion.button>

                            {currentWorkflow && (
                                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                    <span>{currentWorkflow.nodes.length} nodes</span>
                                    <span>•</span>
                                    <span>{currentWorkflow.edges.length} connections</span>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center space-x-2">
                            <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 text-gray-600 dark:text-gray-400 transition-colors">
                                <Settings className="w-4 h-4" />
                            </button>
                        </div>
                    </div>
                </div>

                {/* Canvas */}
                <div
                    ref={canvasRef}
                    className="flex-1 relative overflow-auto bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark-900 dark:to-dark-800"
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onClick={handleCanvasClick}
                    style={{
                        backgroundImage: `
                            radial-gradient(circle at 1px 1px, rgba(0,0,0,0.1) 1px, transparent 0)
                        `,
                        backgroundSize: '20px 20px'
                    }}
                >
                    {/* Connection Mode Overlay */}
                    {connecting && (
                        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50 bg-primary-500 text-white px-4 py-2 rounded-lg shadow-lg">
                            <span className="text-sm font-medium">
                                Click on an input connection to complete the link
                            </span>
                        </div>
                    )}                    {/* Render Nodes */}
                    <AnimatePresence>
                        {currentWorkflow.nodes.map((node) => (
                            <WorkflowNode
                                key={node.id}
                                node={node}
                                isSelected={selectedNode?.id === node.id}
                                onClick={() => handleNodeClick(node.id)}
                                onConnect={handleNodeConnect}
                                onDelete={handleNodeDelete}
                                onPositionChange={handleNodePositionChange}
                                connecting={connecting}
                            />
                        ))}
                    </AnimatePresence>

                    {/* Render Connections */}
                    <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 0 }}>
                        {currentWorkflow.edges.map((edge) => (
                            <ConnectionLine
                                key={edge.id}
                                edge={edge}
                                nodes={currentWorkflow.nodes}
                                onDelete={handleEdgeDelete}
                            />
                        ))}
                    </svg>

                    {/* Empty state overlay */}
                    {currentWorkflow.nodes.length === 0 && (
                        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <div className="text-center">
                                <motion.div
                                    animate={{ y: [0, -10, 0] }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                    className="text-gray-400 dark:text-gray-600 mb-4"
                                >
                                    <Plus className="w-16 h-16 mx-auto" />
                                </motion.div>
                                <p className="text-lg text-gray-500 dark:text-gray-400">
                                    Drag nodes here to start building
                                </p>
                            </div>
                        </div>
                    )}                </div>
            </div>

            {/* Nodes Toolbar */}
            <NodesToolbar />            {/* Node Configuration Panel */}
            <AnimatePresence>
                {selectedNode && (
                    <NodeConfigPanel
                        node={selectedNode}
                        onClose={() => selectNode(null)}
                    />
                )}
            </AnimatePresence>
        </div>
    );
};
