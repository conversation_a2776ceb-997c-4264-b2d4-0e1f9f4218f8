import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, Share2, Wand2, Zap, Star } from 'lucide-react';

export const FloatingElements: React.FC = () => {
  const elements = [
    { Icon: Sparkles, color: 'text-primary-400', delay: 0 },
    { Icon: Video, color: 'text-accent-400', delay: 0.5 },
    { Icon: Share2, color: 'text-secondary-400', delay: 1 },
    { Icon: Wand2, color: 'text-primary-500', delay: 1.5 },
    { Icon: Zap, color: 'text-accent-500', delay: 2 },
    { Icon: Star, color: 'text-secondary-500', delay: 2.5 },
  ];

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
      {elements.map(({ Icon, color, delay }, index) => (
        <motion.div
          key={index}
          className={`absolute ${color} opacity-20`}
          initial={{
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            scale: 0,
            rotate: 0
          }}
          animate={{
            y: [null, -100, null],
            x: [null, Math.random() * 100 - 50, null],
            scale: [0, 1, 0],
            rotate: [0, 360, 720],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            delay: delay + Math.random() * 2,
            ease: "easeInOut"
          }}
          style={{
            left: `${10 + Math.random() * 80}%`,
            top: `${10 + Math.random() * 80}%`,
          }}
        >
          <Icon size={24 + Math.random() * 16} />
        </motion.div>
      ))}
    </div>
  );
};
