import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import {
    Play,
    FileText,
    Mic,
    Image,
    Video,
    Share2,
    Zap,
    CheckCircle,
    Loader,
    X,
    Settings
} from 'lucide-react';
import { useWorkflowStore } from '../../store/workflowStore';
import type { WorkflowNode as WorkflowNodeType } from '../../types';

const iconMap = {
    Play,
    FileText,
    Mic,
    Image,
    Video,
    Share2,
    Zap
};

const typeColorMap = {
    start: 'bg-green-500',
    script: 'bg-purple-500',
    voiceover: 'bg-blue-500',
    visuals: 'bg-pink-500',
    video: 'bg-red-500',
    social: 'bg-indigo-500',
    trigger: 'bg-yellow-500'
};

const typeIconMap = {
    start: 'Play',
    script: 'FileText',
    voiceover: 'Mic',
    visuals: 'Image',
    video: 'Video',
    social: 'Share2',
    trigger: 'Zap'
};

interface WorkflowNodeProps {
    node: WorkflowNodeType;
    isSelected: boolean;
    onClick: () => void;
    onConnect: (nodeId: string, type: 'input' | 'output') => void;
    onDelete: (nodeId: string) => void;
    onPositionChange: (nodeId: string, position: { x: number; y: number }) => void;
    connecting: { nodeId: string; type: 'output' } | null;
}

export const WorkflowNode: React.FC<WorkflowNodeProps> = ({
    node,
    isSelected,
    onClick,
    onConnect,
    onDelete,
    onPositionChange,
    connecting
}) => {
    const [isHovered, setIsHovered] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const nodeRef = useRef<HTMLDivElement>(null);
    const { updateNode } = useWorkflowStore();

    const IconComponent = iconMap[typeIconMap[node.type] as keyof typeof iconMap] || Zap;
    const nodeColor = typeColorMap[node.type] || 'bg-gray-500'; const handleNodeClick = (e: React.MouseEvent) => {
        // Prevent dragging when clicking on connection points or buttons
        if ((e.target as HTMLElement).closest('.connection-point') ||
            (e.target as HTMLElement).closest('button')) {
            return;
        }
        onClick();
    };

    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete(node.id);
    };

    const handleConnectionPointClick = (type: 'input' | 'output', e: React.MouseEvent) => {
        e.stopPropagation();
        onConnect(node.id, type);
    };

    const canConnect = (type: 'input' | 'output') => {
        if (!connecting) return true;

        // Can't connect to same node
        if (connecting.nodeId === node.id) return false;

        // Can only connect output to input
        if (connecting.type === 'output' && type === 'input') return true;

        return false;
    };

    // Don't show input on start node or output on social node
    const hasInput = node.type !== 'start';
    const hasOutput = node.type !== 'social'; return (
        <motion.div
            ref={nodeRef} initial={{ opacity: 0, scale: 0.8 }}
            animate={{
                opacity: 1,
                scale: 1,
                x: node.position.x,
                y: node.position.y
            }}
            exit={{ opacity: 0, scale: 0.8 }}
            drag
            dragMomentum={false}
            dragElastic={0}
            dragConstraints={false} onDragStart={() => setIsDragging(true)}
            onDragEnd={(_, info) => {
                setIsDragging(false);
                // Calculate the final position based on the original position and offset
                const finalPosition = {
                    x: Math.max(0, node.position.x + info.offset.x),
                    y: Math.max(0, node.position.y + info.offset.y)
                };
                updateNode(node.id, { position: finalPosition });
            }}
            whileHover={{ scale: isSelected ? 1.05 : 1.02 }}
            whileDrag={{ scale: 1.1, zIndex: 50 }}
            className={`absolute select-none cursor-move ${isDragging ? 'z-50' : isSelected ? 'z-20' : 'z-10'}`} style={{
                left: 0,
                top: 0
            }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
        >{/* Input Connection Point */}
            {hasInput && (
                <div
                    className={`connection-point absolute -top-3 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full border-2 border-white shadow-lg cursor-pointer transition-all z-10 ${connecting && canConnect('input')
                        ? 'bg-green-400 hover:bg-green-500 scale-125'
                        : connecting && !canConnect('input')
                            ? 'bg-red-400 cursor-not-allowed'
                            : 'bg-gray-400 hover:bg-blue-500'
                        }`}
                    onClick={(e) => handleConnectionPointClick('input', e)}
                    title={connecting ? "Connect here" : "Input connection"}
                />
            )}            {/* Output Connection Point */}
            {hasOutput && (
                <div
                    className={`connection-point absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full border-2 border-white shadow-lg cursor-pointer transition-all z-10 ${connecting && canConnect('output')
                        ? 'bg-green-400 hover:bg-green-500 scale-125'
                        : connecting && !canConnect('output')
                            ? 'bg-red-400 cursor-not-allowed'
                            : 'bg-gray-400 hover:bg-blue-500'
                        }`}
                    onClick={(e) => handleConnectionPointClick('output', e)}
                    title={connecting ? "Start connection" : "Output connection"}
                />
            )}{/* Node Body */}
            <div
                className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-xl border-2 transition-all cursor-pointer ${node.data.active
                    ? 'border-yellow-400 shadow-yellow-200 dark:shadow-yellow-900/50 animate-pulse'
                    : node.data.completed
                        ? 'border-green-400 shadow-green-200 dark:shadow-green-900/50'
                        : isSelected
                            ? 'border-blue-500 shadow-blue-200 dark:shadow-blue-900/50 scale-105'
                            : isHovered
                                ? 'border-gray-300 dark:border-gray-600 shadow-lg scale-102'
                                : 'border-gray-200 dark:border-gray-700'
                    }`}
                style={{
                    width: '180px',
                    minHeight: '100px'
                }}
                onClick={handleNodeClick}
            >
                {/* Delete Button */}
                {(isHovered || isSelected) && (
                    <motion.button
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        whileHover={{ scale: 1.1 }}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg z-20"
                        onClick={handleDelete}
                        title="Delete node"
                    >
                        <X className="w-3 h-3" />
                    </motion.button>
                )}

                {/* Settings Button */}
                {(isHovered || isSelected) && (
                    <motion.button
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        whileHover={{ scale: 1.1 }}
                        className="absolute -top-2 -left-2 w-6 h-6 bg-gray-500 hover:bg-gray-600 text-white rounded-full flex items-center justify-center shadow-lg z-20"
                        onClick={(e) => {
                            e.stopPropagation();
                            onClick(); // This will select the node and open config panel
                        }}
                        title="Configure node"
                    >
                        <Settings className="w-3 h-3" />
                    </motion.button>
                )}

                {/* Node Header */}
                <div className={`${nodeColor} rounded-t-xl p-4 flex items-center justify-center relative`}>
                    <IconComponent className="w-7 h-7 text-white" />

                    {/* Status Indicator */}
                    <div className="absolute top-2 right-2">
                        {node.data.active ? (
                            <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                className="w-5 h-5 bg-yellow-400 rounded-full flex items-center justify-center"
                            >
                                <Loader className="w-3 h-3 text-white" />
                            </motion.div>
                        ) : node.data.completed ? (
                            <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                                <CheckCircle className="w-3 h-3 text-white" />
                            </div>
                        ) : (
                            <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded-full" />
                        )}
                    </div>
                </div>

                {/* Node Content */}
                <div className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm mb-2 text-center">
                        {node.data.label}
                    </h3>
                    {node.data.description && (
                        <p className="text-xs text-gray-600 dark:text-gray-400 text-center leading-relaxed">
                            {node.data.description}
                        </p>
                    )}

                    {/* Configuration Status */}
                    {Object.keys(node.data.config || {}).length > 0 && (
                        <div className="mt-2 flex justify-center">
                            <div className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs rounded-full">
                                Configured
                            </div>
                        </div>
                    )}
                </div>

                {/* Selection Glow */}
                {isSelected && (
                    <motion.div
                        className="absolute inset-0 rounded-xl pointer-events-none"
                        style={{
                            boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.5)',
                        }}
                        animate={{
                            boxShadow: [
                                '0 0 0 3px rgba(59, 130, 246, 0.5)',
                                '0 0 0 6px rgba(59, 130, 246, 0.3)',
                                '0 0 0 3px rgba(59, 130, 246, 0.5)',
                            ]
                        }}
                        transition={{
                            duration: 2,
                            repeat: Infinity,
                            ease: "easeInOut"
                        }}
                    />
                )}
            </div>
        </motion.div>
    );
};