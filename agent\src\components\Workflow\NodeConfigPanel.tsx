import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, Save, Settings } from 'lucide-react';
import { useWorkflowStore } from '../../store/workflowStore';
import type { WorkflowNode, NodeConfigField } from '../../types';

interface NodeConfigPanelProps {
    node: WorkflowNode;
    onClose: () => void;
}

export const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({ node, onClose }) => {
    const { nodeTypes, updateNode } = useWorkflowStore();
    const nodeTypeConfig = nodeTypes.find(nt => nt.type === node.type);

    const [config, setConfig] = useState(node.data.config || {});
    const [hasChanges, setHasChanges] = useState(false);

    const handleConfigChange = (key: string, value: any) => {
        setConfig(prev => ({ ...prev, [key]: value }));
        setHasChanges(true);
    };

    const handleSave = () => {
        updateNode(node.id, {
            data: {
                ...node.data,
                config
            }
        });
        setHasChanges(false);
    };

    const renderConfigField = (field: NodeConfigField) => {
        const value = config[field.key] || '';

        switch (field.type) {
            case 'text':
                return (
                    <input
                        type="text"
                        value={value}
                        onChange={(e) => handleConfigChange(field.key, e.target.value)}
                        placeholder={field.placeholder}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        required={field.required}
                    />
                );

            case 'textarea':
                return (
                    <textarea
                        value={value}
                        onChange={(e) => handleConfigChange(field.key, e.target.value)}
                        placeholder={field.placeholder}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                        required={field.required}
                    />
                );

            case 'select':
                return (
                    <select
                        value={value}
                        onChange={(e) => handleConfigChange(field.key, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        required={field.required}
                    >
                        <option value="">Select {field.label}</option>
                        {field.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                );

            case 'checkbox':
                return (
                    <label className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            checked={!!value}
                            onChange={(e) => handleConfigChange(field.key, e.target.checked)}
                            className="w-4 h-4 text-primary-500 border-gray-300 dark:border-dark-600 rounded focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                            {field.label}
                        </span>
                    </label>
                );

            case 'number':
                return (
                    <input
                        type="number"
                        value={value}
                        onChange={(e) => handleConfigChange(field.key, Number(e.target.value))}
                        placeholder={field.placeholder}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        required={field.required}
                    />
                );

            default:
                return null;
        }
    };

    return (
        <motion.div
            initial={{ x: 320, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 320, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-80 bg-white dark:bg-dark-800 border-l border-gray-200 dark:border-dark-700 z-50 flex flex-col shadow-2xl"
        >
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-dark-700">
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                        <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                            Node Configuration
                        </h2>
                    </div>
                    <button
                        onClick={onClose}
                        className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
                    >
                        <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                </div>
                <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 ${nodeTypeConfig?.color || 'bg-gray-500'} rounded-full`} />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {node.data.label}
                    </span>
                </div>
            </div>

            {/* Configuration Form */}
            <div className="flex-1 overflow-y-auto p-4">
                {nodeTypeConfig?.configFields && nodeTypeConfig.configFields.length > 0 ? (
                    <div className="space-y-4">
                        {nodeTypeConfig.configFields.map((field) => (
                            <div key={field.key}>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    {field.label}
                                    {field.required && <span className="text-red-500 ml-1">*</span>}
                                </label>
                                {renderConfigField(field)}
                                {field.description && (
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        {field.description}
                                    </p>
                                )}
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <Settings className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">
                            This node doesn't have any configuration options.
                        </p>
                    </div>
                )}
            </div>

            {/* Footer */}
            {nodeTypeConfig?.configFields && nodeTypeConfig.configFields.length > 0 && (
                <div className="p-4 border-t border-gray-200 dark:border-dark-700">
                    <div className="flex space-x-2">
                        <motion.button
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={handleSave}
                            disabled={!hasChanges}
                            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg font-medium transition-colors ${hasChanges
                                ? 'bg-primary-500 hover:bg-primary-600 text-white'
                                : 'bg-gray-200 dark:bg-dark-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                                }`}
                        >
                            <Save className="w-4 h-4" />
                            <span>Save</span>
                        </motion.button>
                        <motion.button
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={onClose}
                            className="px-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                        >
                            Cancel
                        </motion.button>
                    </div>
                    {hasChanges && (
                        <p className="text-xs text-amber-600 dark:text-amber-400 mt-2 text-center">
                            You have unsaved changes
                        </p>
                    )}
                </div>
            )}
        </motion.div>
    );
};
