import React from 'react';
import { motion } from 'framer-motion';
import type { WorkflowEdge, WorkflowNode } from '../../types';

interface ConnectionLineProps {
    edge: WorkflowEdge;
    nodes: WorkflowNode[];
    onDelete?: (edgeId: string) => void;
}

export const ConnectionLine: React.FC<ConnectionLineProps> = ({ edge, nodes, onDelete }) => {
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);

    if (!sourceNode || !targetNode) return null;

    // Calculate connection points (center bottom of source, center top of target)
    const sourceX = sourceNode.position.x + 90; // Half of 180px node width
    const sourceY = sourceNode.position.y + 100; // Bottom of node
    const targetX = targetNode.position.x + 90; // Half of 180px node width  
    const targetY = targetNode.position.y; // Top of node

    // Calculate control points for smooth curve
    const controlPointOffset = Math.max(50, Math.abs(targetY - sourceY) * 0.3);
    const sourceControlY = sourceY + controlPointOffset;
    const targetControlY = targetY - controlPointOffset;

    const pathData = `M ${sourceX} ${sourceY} C ${sourceX} ${sourceControlY}, ${targetX} ${targetControlY}, ${targetX} ${targetY}`;

    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (onDelete) {
            onDelete(edge.id);
        }
    };

    // Calculate midpoint for delete button
    const midX = (sourceX + targetX) / 2;
    const midY = (sourceY + targetY) / 2;
    return (
        <>
            {/* Connection Path */}
            <motion.path
                d={pathData}
                stroke="rgb(59 130 246)"
                strokeWidth="3"
                fill="none"
                markerEnd={`url(#arrowhead-${edge.id})`}
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ pathLength: 1, opacity: 1 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className={`hover:stroke-blue-600 cursor-pointer pointer-events-auto ${edge.animated ? "animate-pulse" : ""}`}
                strokeDasharray={edge.animated ? "5,5" : "none"}
            />

            {/* Invisible thicker path for easier hovering */}
            <path
                d={pathData}
                stroke="transparent"
                strokeWidth="12"
                fill="none"
                className="cursor-pointer hover:stroke-blue-200 pointer-events-auto"
                onClick={handleDelete}
            />

            {/* Delete button on hover */}
            <motion.g
                initial={{ opacity: 0, scale: 0 }}
                whileHover={{ opacity: 1, scale: 1 }}
                className="cursor-pointer pointer-events-auto"
                onClick={handleDelete}
            >
                <circle
                    cx={midX}
                    cy={midY}
                    r="8"
                    fill="rgb(239 68 68)"
                    className="hover:fill-red-600"
                />
                <text
                    x={midX}
                    y={midY + 1}
                    textAnchor="middle"
                    className="text-xs fill-white font-bold pointer-events-none"
                >
                    ×
                </text>
            </motion.g>

            {/* Arrow marker */}
            <defs>
                <marker
                    id={`arrowhead-${edge.id}`}
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                    markerUnits="strokeWidth"
                >
                    <polygon
                        points="0 0, 10 3.5, 0 7"
                        fill="rgb(59 130 246)"
                    />
                </marker>
            </defs>

            {/* Animated data flow */}
            {edge.animated && (
                <motion.circle
                    r="3"
                    fill="rgb(34 197 94)"
                    initial={{ offsetDistance: "0%" }}
                    animate={{ offsetDistance: "100%" }}
                    transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "linear"
                    }}
                    style={{
                        offsetPath: `path('${pathData}')`,
                    }}
                />
            )}
        </>
    );
};
