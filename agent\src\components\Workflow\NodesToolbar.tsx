import React from 'react';
import {
    Play,
    FileText,
    Mic,
    Image,
    Video,
    Share2,
    Zap
} from 'lucide-react';
import { useWorkflowStore } from '../../store/workflowStore';

export const NodesToolbar: React.FC = () => {
    const { nodeTypes, setDraggedNodeType } = useWorkflowStore();

    const iconMap = {
        Play,
        FileText,
        Mic,
        Image,
        Video,
        Share2,
        Zap
    };

    const handleDragStart = (nodeType: string, e: React.DragEvent) => {
        setDraggedNodeType(nodeType);
        e.dataTransfer.effectAllowed = 'copy';
    };

    return (
        <div className="w-80 bg-white dark:bg-dark-900 border-l border-gray-200 dark:border-dark-700 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-dark-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Workflow Nodes
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Drag nodes to the canvas to build your workflow
                </p>
            </div>

            {/* Node Types */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {nodeTypes.map((nodeType) => {
                    const IconComponent = iconMap[nodeType.icon as keyof typeof iconMap] || Zap;

                    return (<div
                        key={nodeType.type}
                        draggable
                        onDragStart={(e: React.DragEvent) => handleDragStart(nodeType.type, e)}
                        onDragEnd={() => setDraggedNodeType(null)}
                        className="flex items-center space-x-3 p-4 bg-gray-50 dark:bg-dark-800 rounded-xl cursor-grab active:cursor-grabbing hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 border border-gray-200 dark:border-dark-600 hover:border-primary-300 dark:hover:border-primary-600 hover:scale-105 transform"
                    >
                        <div className={`w-12 h-12 ${nodeType.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg`}>
                            <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 dark:text-gray-100 truncate">
                                {nodeType.label}
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                {nodeType.description}
                            </p>
                            <div className="flex items-center space-x-2 mt-2">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-dark-700 text-gray-800 dark:text-gray-300">
                                    {nodeType.inputs} inputs
                                </span>
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-dark-700 text-gray-800 dark:text-gray-300">
                                    {nodeType.outputs} outputs
                                </span>
                            </div>
                        </div>
                    </div>
                    );
                })}
            </div>

            {/* Tips */}
            <div className="p-4 border-t border-gray-200 dark:border-dark-700">
                <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-primary-900 dark:text-primary-100 mb-1">
                        💡 Tip
                    </h4>
                    <p className="text-xs text-primary-700 dark:text-primary-300">
                        Start with a "Start" node, then drag and connect other nodes to build your content workflow.
                    </p>
                </div>
            </div>
        </div>
    );
};
