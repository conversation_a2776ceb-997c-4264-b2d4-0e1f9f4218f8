import React from 'react';
import { motion } from 'framer-motion';
import { Trash2, Co<PERSON>, Settings } from 'lucide-react';

interface NodeToolbarProps {
    nodeId: string;
    onDelete: () => void;
    onDuplicate: () => void;
    onConfigure: () => void;
}

export const NodeToolbar: React.FC<NodeToolbarProps> = ({
    nodeId,
    onDelete,
    onDuplicate,
    onConfigure
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute -top-12 left-0 flex items-center space-x-1 bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-600 rounded-lg p-1 shadow-lg"
        >
            <button
                onClick={onConfigure}
                className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-dark-700 text-gray-600 dark:text-gray-400 transition-colors"
                title="Configure"
            >
                <Settings className="w-4 h-4" />
            </button>
            <button
                onClick={onDuplicate}
                className="p-1.5 rounded hover:bg-gray-100 dark:hover:bg-dark-700 text-gray-600 dark:text-gray-400 transition-colors"
                title="Duplicate"
            >
                <Copy className="w-4 h-4" />
            </button>
            <button
                onClick={onDelete}
                className="p-1.5 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 transition-colors"
                title="Delete"
            >
                <Trash2 className="w-4 h-4" />
            </button>
        </motion.div>
    );
};
