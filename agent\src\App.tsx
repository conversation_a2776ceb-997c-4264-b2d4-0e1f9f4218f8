import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Toaster } from 'react-hot-toast';
import { Sparkles } from 'lucide-react';
import { useAuthStore } from './store/authStore';
import { Sidebar } from './components/Layout/Sidebar';
import { WorkflowBuilder } from './components/Workflow/WorkflowBuilder';
import { LoginForm } from './components/Auth/LoginForm';
import { RegisterForm } from './components/Auth/RegisterForm';
import { FloatingElements } from './components/Effects/FloatingElements';

function App() {
  const { isAuthenticated } = useAuthStore();
  const [isDark, setIsDark] = useState(false);
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');

  // Initialize theme
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const shouldBeDark = savedTheme === 'dark' || (!savedTheme && prefersDark);

    setIsDark(shouldBeDark);
    document.documentElement.classList.toggle('dark', shouldBeDark);
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    localStorage.setItem('theme', newTheme ? 'dark' : 'light');
    document.documentElement.classList.toggle('dark', newTheme);
  };

  const toggleAuthMode = () => {
    setAuthMode(authMode === 'login' ? 'register' : 'login');
  };

  // Authentication Screen
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-900 flex items-center justify-center p-4">
        {/* Floating Elements */}
        <FloatingElements />

        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-primary-200/20 to-accent-200/20 rounded-full"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-secondary-200/20 to-primary-200/20 rounded-full"
          />
        </div>

        {/* Auth Form */}
        <div className="relative z-10 w-full max-w-md">
          <div className="bg-white/80 dark:bg-dark-900/80 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-dark-700/50">
            <AnimatePresence mode="wait">
              {authMode === 'login' ? (
                <LoginForm key="login" onToggleMode={toggleAuthMode} />
              ) : (
                <RegisterForm key="register" onToggleMode={toggleAuthMode} />
              )}
            </AnimatePresence>
          </div>
        </div>

        <Toaster
          position="top-right"
          toastOptions={{
            className: 'dark:bg-dark-800 dark:text-white',
          }}
        />
      </div>
    );
  }

  // Main Application
  return (
    <div className="h-screen bg-white dark:bg-dark-900 flex overflow-hidden">
      {/* Sidebar */}
      {/* Sidebar - Always visible */}
      <Sidebar
        isDark={isDark}
        onThemeToggle={toggleTheme}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <header className="bg-white dark:bg-dark-900 border-b border-gray-200 dark:border-dark-700 px-4 py-3 flex items-center justify-center shrink-0">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              ContentAI
            </h1>
          </div>
        </header>

        {/* Workflow Builder */}
        <main className="flex-1 overflow-auto">
          <WorkflowBuilder />
        </main>
      </div>

      <Toaster
        position="top-right"
        toastOptions={{
          className: 'dark:bg-dark-800 dark:text-white',
        }}
      />
    </div>
  );
}

export default App;
