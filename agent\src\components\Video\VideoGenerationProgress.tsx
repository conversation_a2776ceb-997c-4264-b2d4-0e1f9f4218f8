import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Video, Mic, Music, Sparkles, Check, AlertCircle } from 'lucide-react';

interface VideoGenerationProgressProps {
  isVisible: boolean;
  onComplete?: () => void;
}

interface Step {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  duration: number;
  status: 'pending' | 'active' | 'complete' | 'error';
}

export const VideoGenerationProgress: React.FC<VideoGenerationProgressProps> = ({
  isVisible,
  onComplete
}) => {
  const [steps, setSteps] = useState<Step[]>([
    {
      id: 'script',
      label: 'Generating script',
      icon: Sparkles,
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'voiceover',
      label: 'Creating AI voiceover',
      icon: Mic,
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'music',
      label: 'Adding background music',
      icon: Music,
      duration: 1500,
      status: 'pending'
    },
    {
      id: 'video',
      label: 'Rendering video',
      icon: Video,
      duration: 4000,
      status: 'pending'
    }
  ]);

  const [currentStepIndex, setCurrentStepIndex] = useState(-1);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      setCurrentStepIndex(-1);
      setProgress(0);
      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));
      return;
    }

    let timeoutId: NodeJS.Timeout;
    let intervalId: NodeJS.Timeout;

    const processNextStep = (index: number) => {
      if (index >= steps.length) {
        onComplete?.();
        return;
      }

      // Mark current step as active
      setSteps(prev => prev.map((step, i) => ({
        ...step,
        status: i === index ? 'active' : i < index ? 'complete' : 'pending'
      })));

      setCurrentStepIndex(index);
      setProgress(0);

      // Simulate progress for current step
      const stepDuration = steps[index].duration;
      const progressInterval = 50;
      const progressIncrement = (progressInterval / stepDuration) * 100;

      intervalId = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + progressIncrement;
          if (newProgress >= 100) {
            clearInterval(intervalId);
            // Mark step as complete and move to next
            setSteps(prevSteps => prevSteps.map((step, i) => ({
              ...step,
              status: i === index ? 'complete' : step.status
            })));
            
            timeoutId = setTimeout(() => processNextStep(index + 1), 500);
            return 100;
          }
          return newProgress;
        });
      }, progressInterval);
    };

    processNextStep(0);

    return () => {
      clearTimeout(timeoutId);
      clearInterval(intervalId);
    };
  }, [isVisible, onComplete]);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ y: 20 }}
          animate={{ y: 0 }}
          className="bg-white dark:bg-dark-800 rounded-2xl p-8 max-w-md w-full shadow-2xl"
        >
          <div className="text-center mb-8">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 bg-gradient-to-r from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center mx-auto mb-4"
            >
              <Video className="w-8 h-8 text-white" />
            </motion.div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              Creating Your Video
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we generate your content...
            </p>
          </div>

          <div className="space-y-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.status === 'active';
              const isComplete = step.status === 'complete';
              const isPending = step.status === 'pending';

              return (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center space-x-4 p-4 rounded-lg transition-all ${
                    isActive 
                      ? 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800' 
                      : isComplete
                      ? 'bg-green-50 dark:bg-green-900/20'
                      : 'bg-gray-50 dark:bg-dark-700'
                  }`}
                >
                  <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                    isActive 
                      ? 'bg-primary-500 text-white' 
                      : isComplete
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-300 dark:bg-dark-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {isComplete ? (
                      <Check className="w-5 h-5" />
                    ) : isActive ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Icon className="w-5 h-5" />
                      </motion.div>
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </div>

                  <div className="flex-1">
                    <p className={`font-medium ${
                      isActive || isComplete 
                        ? 'text-gray-900 dark:text-gray-100' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.label}
                    </p>
                    
                    {isActive && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                          <motion.div
                            className="bg-primary-500 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${progress}%` }}
                            transition={{ duration: 0.1 }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {Math.round(progress)}% complete
                        </p>
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              This usually takes 30-60 seconds
            </p>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
