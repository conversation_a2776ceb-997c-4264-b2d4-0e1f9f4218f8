// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  subscription?: 'free' | 'pro' | 'enterprise';
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Chat and Message Types
export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

// Content Generation Types
export interface ContentPrompt {
  text: string;
  tone?: 'professional' | 'casual' | 'energetic' | 'friendly';
  targetAudience?: string;
  platform?: SocialPlatform[];
  duration?: number; // in seconds
}

export interface GeneratedContent {
  id: string;
  prompt: ContentPrompt;
  script: string;
  voiceoverUrl?: string;
  videoUrl?: string;
  thumbnailUrl?: string;
  status: 'generating' | 'ready' | 'error';
  createdAt: Date;
}

// Social Media Types
export type SocialPlatform =
  | 'twitter'
  | 'instagram'
  | 'tiktok'
  | 'facebook'
  | 'youtube'
  | 'linkedin';

export interface SocialAccount {
  platform: SocialPlatform;
  username: string;
  isConnected: boolean;
  accessToken?: string;
  refreshToken?: string;
}

export interface PostSchedule {
  id: string;
  contentId: string;
  platforms: SocialPlatform[];
  scheduledAt: Date;
  status: 'scheduled' | 'posting' | 'posted' | 'failed';
  caption?: string;
}

// Video Generation Types
export interface VideoSettings {
  resolution: '720p' | '1080p' | '4k';
  aspectRatio: '16:9' | '9:16' | '1:1';
  backgroundMusic?: string;
  voiceSettings: {
    voice: string;
    speed: number;
    pitch: number;
  };
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  accentColor: string;
}

// Analytics Types
export interface Analytics {
  totalContent: number;
  totalPosts: number;
  engagement: {
    likes: number;
    shares: number;
    comments: number;
  };
  platformStats: Record<SocialPlatform, {
    posts: number;
    engagement: number;
  }>;
}

// Workflow Node Types
export interface WorkflowNode {
  id: string;
  type: 'start' | 'script' | 'voiceover' | 'visuals' | 'video' | 'social' | 'trigger';
  position: { x: number; y: number };
  data: {
    label: string;
    description?: string;
    config?: Record<string, any>;
    completed?: boolean;
    active?: boolean;
  };
  inputs?: NodeConnection[];
  outputs?: NodeConnection[];
}

export interface NodeConnection {
  id: string;
  type: 'input' | 'output';
  position: 'top' | 'bottom' | 'left' | 'right';
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  animated?: boolean;
}

export interface WorkflowData {
  id: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  createdAt: Date;
  updatedAt: Date;
}

export interface NodeTypeConfig {
  type: string;
  label: string;
  description: string;
  icon: string;
  color: string;
  category: 'input' | 'processing' | 'output' | 'social';
  inputs: number;
  outputs: number;
  configFields?: NodeConfigField[];
}

export interface NodeConfigField {
  key: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'number';
  required?: boolean;
  options?: { label: string; value: string }[];
  placeholder?: string;
  description?: string;
}

export interface ExecutionResult {
  nodeId: string;
  status: 'success' | 'error' | 'running';
  data?: any;
  error?: string;
  timestamp: Date;
}
